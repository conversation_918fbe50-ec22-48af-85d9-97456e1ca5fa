package com.tianji.promotion.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import com.tianji.common.autoconfigure.mq.RabbitMqHelper;
import com.tianji.common.constants.MqConstants;
import com.tianji.common.exceptions.BadRequestException;
import com.tianji.common.exceptions.BizIllegalException;
import com.tianji.common.utils.BeanUtils;
import com.tianji.common.utils.StringUtils;
import com.tianji.common.utils.UserContext;
import com.tianji.promotion.constants.PromotionConstants;
import com.tianji.promotion.domain.dto.UserCouponDTO;
import com.tianji.promotion.domain.po.Coupon;
import com.tianji.promotion.domain.po.ExchangeCode;
import com.tianji.promotion.domain.po.UserCoupon;
import com.tianji.promotion.enums.CouponStatus;
import com.tianji.promotion.enums.ExchangeCodeStatus;
import com.tianji.promotion.mapper.CouponMapper;
import com.tianji.promotion.mapper.UserCouponMapper;
import com.tianji.promotion.service.IExchangeCodeService;
import com.tianji.promotion.service.IUserCouponService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianji.promotion.utils.CodeUtil;
import com.tianji.promotion.utils.MyLockUtils.MyLock;
import com.tianji.promotion.utils.MyLockUtils.enums.MyLockStrategy;
import com.tianji.promotion.utils.MyLockUtils.enums.MyLockType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <p>
 * 用户领取优惠券的记录，是真正使用的优惠券信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserCouponServiceImpl extends ServiceImpl<UserCouponMapper, UserCoupon> implements IUserCouponService {

    private final CouponMapper couponMapper;
    private final IExchangeCodeService exchangeCodeService;
    private final StringRedisTemplate redisTemplate;
    private final RedissonClient redisson;
    private final RabbitMqHelper rabbitMqHelper;

    /**
     * 用户领取优惠券
     * @param id 优惠券id
     */
    @Override
    @MyLock(name = "lock:coupon:uid:#{id}")
    public void receiveCoupon(Long id) {
        log.debug("receiveCoupon 用户领取优惠券：{}", id);
        //1.根据id查询优惠券信息做相关校验
        if(id == null){
            throw new BadRequestException("非法参数");
        }
//        Coupon coupon = couponMapper.selectById(id);
        // 缓存查询
        Coupon coupon = queryCouponByCache(id);
        if(coupon==null){
            throw new BadRequestException("优惠券不存在");
        }
        /*if(coupon.getStatus() != CouponStatus.ISSUING){
            throw new BadRequestException("该优惠券状态不是正在发放");
        }*/
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(coupon.getIssueBeginTime()) || now.isAfter(coupon.getIssueEndTime())){
            throw new BadRequestException("该优惠券不在发放时间");
        }
        if (coupon.getTotalNum() <= 0){
            throw new BadRequestException("该优惠券已发放完毕");
        }
        Long userId = UserContext.getUser();
        // 获取当前用户已经领取的优惠券
        /*synchronized (userId.toString().intern()) {
            // 从aop 获取service
            IUserCouponService userCouponService = (IUserCouponService) AopContext.currentProxy();
            userCouponService.checkAndCreateUserCoupon(coupon, userId, null);
        }*/
        // 从aop 获取service
        /*IUserCouponService userCouponService = (IUserCouponService) AopContext.currentProxy();
        userCouponService.checkAndCreateUserCoupon(coupon, userId, null);*/

        // 统计已经领取的数量
        String key = PromotionConstants.USER_COUPON_CACHE_KEY_PREFIX + id;
        // 本次领取的数量
        Long increment = redisTemplate.opsForHash().increment(key, userId.toString(), 1);
        // 校验已领取数量
        if (increment > coupon.getUserLimit()){
            throw new BizIllegalException("超出限领数量");
        }
        // 修改优惠券库存
        String couponKey = PromotionConstants.COUPON_CACHE_KEY_PREFIX + id;
        redisTemplate.opsForHash().increment(couponKey,"totalNum",-1);
        // 发送消息
        UserCouponDTO msg = new UserCouponDTO();
        msg.setUserId(userId);
        msg.setCouponId(id);
        rabbitMqHelper.send(MqConstants.Exchange.PROMOTION_EXCHANGE,MqConstants.Key.COUPON_RECEIVE,msg);

    }

    // 缓存查询
    private Coupon queryCouponByCache(Long id) {
        // 拼接key
        String key = PromotionConstants.COUPON_CACHE_KEY_PREFIX + id;
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
        return BeanUtils.mapToBean(entries, Coupon.class, false, CopyOptions.create());
    }

    @Override
//    @Transactional
    public void exchangeCoupon(String code) {
        // 校验code
        if (StringUtils.isBlank(code)){
            throw new BadRequestException("非法参数");
        }
        // 解析兑换码的id
        long serialNum = CodeUtil.parseCode(code);
        // 获取兑换码
        boolean result = exchangeCodeService.updateExchangeCodeMark(serialNum,true);
        if (result){
            throw new BadRequestException("兑换码已使用");
        }
        //
        try {
            // 确认是否存在
            ExchangeCode exchangeCode = exchangeCodeService.getById(serialNum);
            if (exchangeCode == null){
                throw new BadRequestException("兑换码不存在");
            }
            // 是否过期
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expiredTime = exchangeCode.getExpiredTime();
            if (now.isAfter(expiredTime)){
                throw new BadRequestException("兑换码已过期");
            }
            // 校验并生成用户券
            Long userId = UserContext.getUser();
            Coupon coupon = couponMapper.selectById(exchangeCode.getExchangeTargetId());
            if (coupon == null){
                throw new BadRequestException("兑换码已失效");
            }

            /*// 先开启锁 然后再执行事务
            synchronized (userId.toString().intern()) {
                checkAndCreateUserCoupon(coupon, userId, serialNum);
            }*/
            checkAndCreateUserCoupon(coupon, userId,serialNum);


        } catch (Exception e) {
            exchangeCodeService.updateExchangeCodeMark(serialNum,false);
            throw new RuntimeException(e);
        }
    }

    // 创建用户优惠券
    @Transactional // 控制锁
    @MyLock(name = "lock:coupon:uid:#{userId}",waitTime = 1,leaseTime = 5, LockType = MyLockType.RE_ENTRANT_LOCK,lockStrategy = MyLockStrategy.FAIL_FAST)
    public void checkAndCreateUserCoupon(Coupon coupon, Long userId, Long serialNum) {
        Long count = this.lambdaQuery()
                .eq(UserCoupon::getUserId, userId)
                .eq(UserCoupon::getCouponId, coupon.getId())
                .count();
        if (count >= coupon.getUserLimit()){
            throw new BadRequestException("该优惠券已领取");
        }
        // 增加领取数量
        couponMapper.incrIssueNum(coupon.getId());
        // 生成用户券
        saveUserCoupon(userId, coupon);
        // 修改兑换码
        if (serialNum != null){
            exchangeCodeService.lambdaUpdate()
                    .set(ExchangeCode::getUserId, userId)
                    .set(ExchangeCode::getStatus, ExchangeCodeStatus.USED)
                    .eq(ExchangeCode::getId, serialNum)
                    .update();
        }

    }

    /**
     * 检查并创建用户优惠券
     * @param msg 用户优惠券信息，包含用户ID和优惠券ID
     */
    @Transactional
    @Override
    public void checkAndCreateUserCouponNew(UserCouponDTO msg) {
        Coupon coupon = couponMapper.selectById(msg.getCouponId());
        if (coupon == null){
            return;
        }
        // 增加领取数量
        couponMapper.incrIssueNum(coupon.getId());
        // 生成用户券
        saveUserCoupon(msg.getUserId(), coupon);
        // 修改兑换码
        /*if (serialNum != null){
            exchangeCodeService.lambdaUpdate()
                    .set(ExchangeCode::getUserId, userId)
                    .set(ExchangeCode::getStatus, ExchangeCodeStatus.USED)
                    .eq(ExchangeCode::getId, serialNum)
                    .update();
        }*/
    }

    // 保存用户券
    private void saveUserCoupon(Long userId, Coupon coupon) {

        LocalDateTime termBeginTime = coupon.getTermBeginTime();
        LocalDateTime termEndTime = coupon.getTermEndTime();
        if (termBeginTime == null && termEndTime == null){
            termBeginTime = LocalDateTime.now();
            termEndTime = termBeginTime.plusDays(coupon.getTermDays());
        }
        // 创建用户券
        UserCoupon userCoupon = UserCoupon.builder()
                .userId(userId)
                .couponId(coupon.getId())
                .termBeginTime(termBeginTime)
                .termEndTime(termEndTime)
                .build();

        this.save(userCoupon);
    }



}
